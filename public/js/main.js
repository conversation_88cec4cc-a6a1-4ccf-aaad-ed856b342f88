// 主要JavaScript功能

// Bulma 模态框控制函数
function closeModal(modalId) {
    const modal = document.getElementById(modalId);
    if (modal) {
        modal.remove();
    }
}

// 日期格式化函数 - 避免时区问题
function formatDate(dateString) {
    if (!dateString) return '未设置';
    const date = new Date(dateString);
    // 使用本地时区格式化，避免UTC转换问题
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
}

// 格式化日期时间为本地格式
function formatDateTime(dateString) {
    if (!dateString) return '未知';
    return formatDate(dateString);
}

// 从日期字符串创建本地日期对象（避免时区问题）
function createLocalDate(dateString) {
    if (!dateString) return null;
    // 如果是 YYYY-MM-DD 格式，直接解析为本地日期
    if (dateString.match(/^\d{4}-\d{2}-\d{2}$/)) {
        const [year, month, day] = dateString.split('-').map(Number);
        return new Date(year, month - 1, day);
    }
    return new Date(dateString);
}

// 编辑记录
function editRecord(recordId) {
    // 检查当前用户权限
    const currentPath = window.location.pathname;
    if (currentPath.includes('/staff/')) {
        alert('员工没有编辑权限，如需修改请联系主任或管理员');
        return;
    }

    // 从页面表格中获取记录数据
    const record = getRecordFromPage(recordId);
    if (!record) {
        alert('获取记录信息失败');
        return;
    }

    showEditRecordModal(record, recordId);
}

// 从页面表格中获取记录数据
function getRecordFromPage(recordId) {
    const rows = document.querySelectorAll('table tbody tr');
    for (let row of rows) {
        const editButton = row.querySelector(`button[onclick*="editRecord(${recordId})"]`);
        if (editButton) {
            const cells = row.querySelectorAll('td');
            if (cells.length >= 3) {
                // 根据不同页面的表格结构获取数据
                const currentPath = window.location.pathname;
                if (currentPath.includes('/staff/')) {
                    // 员工页面：值班日期、调休日期（员工无编辑权限，此函数不应被调用）
                    alert('员工没有编辑权限');
                    return null;
                } else if (currentPath.includes('/director/')) {
                    // 主任页面：工号、姓名、值班日期、调休日期、是否自定义调休、操作
                    const dutyDateText = cells[2].textContent.trim();
                    const leaveDateText = cells[3].textContent.trim();
                    const isCustomLeaveText = cells[4].textContent.trim();
                    return {
                        record_id: recordId,
                        duty_date: dutyDateText,
                        leave_date: leaveDateText === '未设置' ? '' : leaveDateText,
                        is_custom_leave: isCustomLeaveText === '是'
                    };
                } else if (currentPath.includes('/admin/')) {
                    // 管理员页面：姓名、值班日期、调休日期、是否自定义调休、操作
                    const dutyDateText = cells[1].textContent.trim();
                    const leaveDateText = cells[2].textContent.trim();
                    const isCustomLeaveText = cells[3].textContent.trim();
                    return {
                        record_id: recordId,
                        duty_date: dutyDateText,
                        leave_date: leaveDateText === '未设置' ? '' : leaveDateText,
                        is_custom_leave: isCustomLeaveText === '是'
                    };
                }
            }
        }
    }
    return null;
}

// 显示编辑记录模态框
function showEditRecordModal(record, recordId) {
    const modal = `
        <div class="modal is-active" id="editRecordModal">
            <div class="modal-background" onclick="closeModal('editRecordModal')"></div>
            <div class="modal-card">
                <header class="modal-card-head">
                    <p class="modal-card-title">编辑值班记录</p>
                    <button class="delete" aria-label="close" onclick="closeModal('editRecordModal')"></button>
                </header>
                <section class="modal-card-body">
                    <form id="editRecordForm">
                        <div class="field">
                            <label class="label" for="editDutyDate">值班日期</label>
                            <div class="control">
                                <input class="input" type="date" id="editDutyDate" value="${record.duty_date}" required>
                            </div>
                        </div>
                        <div class="field">
                            <label class="label" for="editLeaveDate">调休日期</label>
                            <div class="control">
                                <input class="input" type="date" id="editLeaveDate" value="${record.leave_date}">
                            </div>
                            <p class="help">可以为空，表示暂未安排调休</p>
                        </div>

                    </form>
                </section>
                <footer class="modal-card-foot">
                    <button class="button is-primary" onclick="updateRecord(${recordId})">保存修改</button>
                    <button class="button" onclick="closeModal('editRecordModal')">取消</button>
                </footer>
            </div>
        </div>
    `;

    // 移除已存在的模态框
    const existingModal = document.getElementById('editRecordModal');
    if (existingModal) {
        existingModal.remove();
    }

    // 添加模态框到页面
    document.body.insertAdjacentHTML('beforeend', modal);
}

// 更新记录
function updateRecord(recordId) {
    const dutyDate = document.getElementById('editDutyDate').value;
    const leaveDate = document.getElementById('editLeaveDate').value;

    if (!dutyDate) {
        alert('请选择值班日期');
        return;
    }

    // 验证日期格式
    if (leaveDate && new Date(leaveDate) <= new Date(dutyDate)) {
        alert('调休日期不能在值班日期当天或之前');
        return;
    }

    // 确定API路径
    const currentPath = window.location.pathname;
    let apiPath = '';

    if (currentPath.includes('/staff/')) {
        apiPath = '/staff/records';
    } else if (currentPath.includes('/director/')) {
        apiPath = '/director/records';
    } else if (currentPath.includes('/admin/')) {
        apiPath = '/admin/records';
    }

    // 发送更新请求
    makeRequest(`${apiPath}/${recordId}`, 'PUT', {
        dutyDate: dutyDate,
        leaveDate: leaveDate || null
    })
    .then(data => {
        if (data.success) {
            alert('记录更新成功');
            // 关闭模态框
            closeModal('editRecordModal');
            // 刷新页面
            location.reload();
        } else {
            alert('更新失败: ' + data.error);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('更新记录时发生错误');
    });
}

// 删除记录
function deleteRecord(recordId) {
    // 检查当前用户权限
    const currentPath = window.location.pathname;
    if (currentPath.includes('/staff/')) {
        alert('员工没有删除权限，如需删除请联系主任或管理员');
        return;
    }

    if (confirm('确定要删除这条记录吗？')) {
        // 确定API路径
        let apiPath = '';

        if (currentPath.includes('/director/')) {
            apiPath = '/director/records';
        } else if (currentPath.includes('/admin/')) {
            apiPath = '/admin/records';
        }

        makeRequest(`${apiPath}/${recordId}`, 'DELETE')
        .then(data => {
            if (data.success) {
                alert('删除成功');
                location.reload();
            } else {
                alert('删除失败: ' + data.error);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('删除时发生错误');
        });
    }
}

// 显示添加值班记录模态框
function showAddRecordModal() {
    // 首先获取部门员工列表
    const currentPath = window.location.pathname;
    let apiPath = '';

    if (currentPath.includes('/director/')) {
        apiPath = '/director/users';
    } else if (currentPath.includes('/admin/')) {
        apiPath = '/admin/users';
    }

    fetch(apiPath)
        .then(response => response.json())
        .then(users => {
            const modal = `
                <div class="modal is-active" id="addRecordModal">
                    <div class="modal-background" onclick="closeModal('addRecordModal')"></div>
                    <div class="modal-card">
                        <header class="modal-card-head">
                            <p class="modal-card-title">添加值班记录</p>
                            <button class="delete" aria-label="close" onclick="closeModal('addRecordModal')"></button>
                        </header>
                        <section class="modal-card-body">
                            <form id="addRecordForm">
                                <div class="field">
                                    <label class="label" for="addRecordUser">选择员工</label>
                                    <div class="control">
                                        <div class="select is-fullwidth">
                                            <select id="addRecordUser" required>
                                                <option value="">请选择员工</option>
                                                ${users.map(user =>
                                                    `<option value="${user.userid}">${user.job_number} - ${user.name}</option>`
                                                ).join('')}
                                            </select>
                                        </div>
                                    </div>
                                </div>
                                <div class="field">
                                    <label class="label" for="addDutyDate">值班日期</label>
                                    <div class="control">
                                        <input class="input" type="date" id="addDutyDate" required>
                                    </div>
                                </div>
                                <div class="field">
                                    <label class="label" for="addLeaveDate">调休日期</label>
                                    <div class="control">
                                        <input class="input" type="date" id="addLeaveDate">
                                    </div>
                                    <p class="help">可以为空，表示暂未安排调休</p>
                                </div>

                            </form>
                        </section>
                        <footer class="modal-card-foot">
                            <button class="button is-primary" onclick="createDutyRecord()">创建记录</button>
                            <button class="button" onclick="closeModal('addRecordModal')">取消</button>
                        </footer>
                    </div>
                </div>
            `;

            // 移除已存在的模态框
            const existingModal = document.getElementById('addRecordModal');
            if (existingModal) {
                existingModal.remove();
            }

            // 添加模态框到页面
            document.body.insertAdjacentHTML('beforeend', modal);
        })
        .catch(error => {
            console.error('Error fetching users:', error);
            alert('获取员工列表失败');
        });
}

// 创建值班记录
function createDutyRecord() {
    const userid = document.getElementById('addRecordUser').value;
    const dutyDate = document.getElementById('addDutyDate').value;
    const leaveDate = document.getElementById('addLeaveDate').value;

    if (!userid || !dutyDate) {
        alert('请选择员工和值班日期');
        return;
    }

    // 验证日期格式
    if (leaveDate && new Date(leaveDate) <= new Date(dutyDate)) {
        alert('调休日期不能在值班日期当天或之前');
        return;
    }

    // 确定API路径
    const currentPath = window.location.pathname;
    let apiPath = '';

    if (currentPath.includes('/director/')) {
        apiPath = '/director/records';
    } else if (currentPath.includes('/admin/')) {
        apiPath = '/admin/records';
    }

    // 发送创建请求
    makeRequest(apiPath, 'POST', {
        userid: userid,
        dutyDate: dutyDate,
        leaveDate: leaveDate || null
    })
    .then(data => {
        if (data.success) {
            alert('值班记录创建成功');
            // 关闭模态框
            closeModal('addRecordModal');
            // 刷新页面
            location.reload();
        } else {
            alert('创建失败: ' + data.error);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('创建记录时发生错误');
    });
}

// 显示批量导入值班安排模态框
function showImportDutyModal() {
    const modal = `
        <div class="modal is-active" id="importDutyModal">
            <div class="modal-background" onclick="closeModal('importDutyModal')"></div>
            <div class="modal-card" style="width: 90%; max-width: 800px;">
                <header class="modal-card-head">
                    <p class="modal-card-title">批量导入值班安排</p>
                    <button class="delete" aria-label="close" onclick="closeModal('importDutyModal')"></button>
                </header>
                <section class="modal-card-body">
                    <div class="notification is-info">
                        <h6 class="title is-6">导入说明：</h6>
                        <ul>
                            <li>支持Excel文件格式 (.xlsx, .xls)</li>
                            <li>文件大小不超过5MB</li>
                            <li>请使用提供的模板格式</li>
                            <li>工号必须是系统中已存在的用户</li>
                            <li>日期格式：YYYY-MM-DD (如：2024-06-15)</li>
                            <li>调休日期可以为空，系统将自动设置为值班日期的后一天</li>
                            <li>系统将自动判断是否为自定义调休（调休日期不是值班日期后一天即为自定义）</li>
                        </ul>
                    </div>

                    <div class="field">
                        <label class="label">1. 下载导入模板</label>
                        <div class="control">
                            <a href="/director/duty-template" class="button is-primary is-outlined" download>
                                <span class="icon">
                                    <i class="fas fa-download"></i>
                                </span>
                                <span>下载Excel模板</span>
                            </a>
                        </div>
                    </div>

                    <div class="field">
                        <label class="label" for="importDutyFile">2. 选择要导入的Excel文件</label>
                        <div class="control">
                            <input class="input" type="file" id="importDutyFile" accept=".xlsx,.xls">
                        </div>
                    </div>

                    <div id="importDutyProgress" class="is-hidden">
                        <progress class="progress is-primary" max="100">正在导入...</progress>
                    </div>

                    <div id="importDutyResult" class="is-hidden">
                        <div class="notification" id="importDutyAlert">
                            <div id="importDutyMessage"></div>
                            <div id="importDutyDetails" class="mt-2"></div>
                        </div>
                    </div>
                </section>
                <footer class="modal-card-foot">
                    <button class="button is-success" onclick="importDutyRecords()" id="importDutyBtn">
                        <span class="icon">
                            <i class="fas fa-upload"></i>
                        </span>
                        <span>开始导入</span>
                    </button>
                    <button class="button" onclick="closeModal('importDutyModal')">取消</button>
                </footer>
            </div>
        </div>
    `;

    // 移除已存在的模态框
    const existingModal = document.getElementById('importDutyModal');
    if (existingModal) {
        existingModal.remove();
    }

    // 添加模态框到页面
    document.body.insertAdjacentHTML('beforeend', modal);
}

// 批量导入值班记录
function importDutyRecords() {
    const fileInput = document.getElementById('importDutyFile');
    const file = fileInput.files[0];

    if (!file) {
        alert('请选择要导入的Excel文件');
        return;
    }

    // 检查文件类型
    const allowedTypes = [
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'application/vnd.ms-excel'
    ];

    if (!allowedTypes.includes(file.type)) {
        alert('请选择Excel文件 (.xlsx 或 .xls)');
        return;
    }

    // 检查文件大小 (5MB)
    if (file.size > 5 * 1024 * 1024) {
        alert('文件大小不能超过5MB');
        return;
    }

    // 显示进度条
    document.getElementById('importDutyProgress').classList.remove('is-hidden');
    document.getElementById('importDutyResult').classList.add('is-hidden');
    document.getElementById('importDutyBtn').disabled = true;

    // 创建FormData
    const formData = new FormData();
    formData.append('dutyFile', file);

    // 确定API路径
    const currentPath = window.location.pathname;
    let apiPath = '';

    if (currentPath.includes('/director/')) {
        apiPath = '/director/duty-import';
    } else if (currentPath.includes('/admin/')) {
        apiPath = '/admin/duty-import';
    }

    // 发送请求
    fetch(apiPath, {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        // 隐藏进度条
        document.getElementById('importDutyProgress').classList.add('is-hidden');
        document.getElementById('importDutyBtn').disabled = false;

        // 显示结果
        const resultDiv = document.getElementById('importDutyResult');
        const alertDiv = document.getElementById('importDutyAlert');
        const messageDiv = document.getElementById('importDutyMessage');
        const detailsDiv = document.getElementById('importDutyDetails');

        resultDiv.classList.remove('is-hidden');

        if (data.success) {
            alertDiv.className = 'notification is-success';
            messageDiv.textContent = data.message;

            if (data.details && data.details.errorCount > 0) {
                let detailsHtml = `<strong>导入详情：</strong><br>`;
                detailsHtml += `成功：${data.details.successCount} 条记录<br>`;
                detailsHtml += `失败：${data.details.errorCount} 条记录<br>`;

                if (data.details.errors.length > 0) {
                    detailsHtml += `<br><strong>错误信息：</strong><br>`;
                    data.details.errors.forEach(error => {
                        detailsHtml += `• ${error}<br>`;
                    });
                }

                detailsDiv.innerHTML = detailsHtml;
            } else {
                detailsDiv.innerHTML = `<strong>所有记录导入成功！</strong>`;
            }

            // 3秒后刷新页面
            setTimeout(() => {
                location.reload();
            }, 3000);
        } else {
            alertDiv.className = 'notification is-danger';
            messageDiv.textContent = '导入失败：' + data.error;
            detailsDiv.innerHTML = '';
        }
    })
    .catch(error => {
        console.error('Import error:', error);

        // 隐藏进度条
        document.getElementById('importDutyProgress').classList.add('is-hidden');
        document.getElementById('importDutyBtn').disabled = false;

        // 显示错误
        const resultDiv = document.getElementById('importDutyResult');
        const alertDiv = document.getElementById('importDutyAlert');
        const messageDiv = document.getElementById('importDutyMessage');

        resultDiv.classList.remove('is-hidden');
        alertDiv.className = 'notification is-danger';
        messageDiv.textContent = '导入过程中发生错误，请稍后重试';
    });
}

// 查看员工详细统计
function viewStaffDetail(userid, name) {
    // 确定API路径
    const currentPath = window.location.pathname;
    let apiPath = '';

    if (currentPath.includes('/director/')) {
        apiPath = '/director/stats';
    } else if (currentPath.includes('/admin/')) {
        apiPath = '/admin/stats';
    }

    // 获取当前年份
    const currentYear = new Date().getFullYear();

    fetch(`${apiPath}/${userid}?year=${currentYear}`)
        .then(response => response.json())
        .then(stats => {
            showStaffDetailModal(name, stats);
        })
        .catch(error => {
            console.error('Error fetching staff stats:', error);
            alert('获取员工统计信息失败');
        });
}

// 显示员工详细统计模态框
function showStaffDetailModal(name, stats) {
    const modal = `
        <div class="modal is-active" id="staffDetailModal">
            <div class="modal-background" onclick="closeModal('staffDetailModal')"></div>
            <div class="modal-card" style="width: 90%; max-width: 900px;">
                <header class="modal-card-head">
                    <p class="modal-card-title">${name} - 详细统计</p>
                    <button class="delete" aria-label="close" onclick="closeModal('staffDetailModal')"></button>
                </header>
                <section class="modal-card-body">
                    <div class="columns is-multiline mb-3">
                        <div class="column is-3">
                            <div class="card has-text-centered">
                                <div class="card-content">
                                    <h4 class="title is-4 has-text-primary">${stats.totalDutyDays || 0}</h4>
                                    <small>总值班天数</small>
                                </div>
                            </div>
                        </div>
                        <div class="column is-3">
                            <div class="card has-text-centered">
                                <div class="card-content">
                                    <h4 class="title is-4 has-text-success">${stats.totalLeaveDays || 0}</h4>
                                    <small>总调休天数</small>
                                </div>
                            </div>
                        </div>
                        <div class="column is-3">
                            <div class="card has-text-centered">
                                <div class="card-content">
                                    <h4 class="title is-4 has-text-warning">${stats.currentMonthDuty || 0}</h4>
                                    <small>本月值班</small>
                                </div>
                            </div>
                        </div>
                        <div class="column is-3">
                            <div class="card has-text-centered">
                                <div class="card-content">
                                    <h4 class="title is-4 has-text-info">${stats.avgDutyPerMonth || 0}</h4>
                                    <small>月均值班</small>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="columns">
                        <div class="column is-half">
                            <h6 class="title is-6">月度值班分布</h6>
                            <div class="table-container">
                                <table class="table is-narrow is-fullwidth">
                                    <thead>
                                        <tr>
                                            <th>月份</th>
                                            <th>值班天数</th>
                                            <th>调休天数</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        ${generateMonthlyStatsTable(stats.monthlyStats || {})}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                        <div class="column is-half">
                            <h6 class="title is-6">值班类型分析</h6>
                            <div class="field">
                                <label class="label">自定义调休比例</label>
                                <progress class="progress is-primary" value="${stats.customLeaveRate || 0}" max="100"></progress>
                                <small>${stats.customLeaveRate || 0}% (${stats.customLeaveDays || 0}/${stats.totalDutyDays || 0})</small>
                            </div>
                        </div>
                    </div>
                </section>
                <footer class="modal-card-foot">
                    <button class="button" onclick="closeModal('staffDetailModal')">关闭</button>
                </footer>
            </div>
        </div>
    `;

    // 移除已存在的模态框
    const existingModal = document.getElementById('staffDetailModal');
    if (existingModal) {
        existingModal.remove();
    }

    // 添加模态框到页面
    document.body.insertAdjacentHTML('beforeend', modal);
}

// 生成月度统计表格
function generateMonthlyStatsTable(monthlyStats) {
    let html = '';
    for (let month = 1; month <= 12; month++) {
        const stats = monthlyStats[month] || { dutyDays: 0, leaveDays: 0 };
        html += `
            <tr>
                <td>${month}月</td>
                <td><span class="tag is-primary">${stats.dutyDays}</span></td>
                <td><span class="tag is-success">${stats.leaveDays}</span></td>
            </tr>
        `;
    }
    return html;
}

// 年份和月份选择器变化事件
document.addEventListener('DOMContentLoaded', function() {
    const yearSelect = document.getElementById('yearSelect');
    const monthSelect = document.getElementById('monthSelect');

    // 年份选择器事件
    if (yearSelect) {
        yearSelect.addEventListener('change', function() {
            const selectedYear = this.value;
            const currentMonth = monthSelect ? monthSelect.value : new Date().getMonth() + 1;
            updateDateSelection(selectedYear, currentMonth);
        });
    }

    // 月份选择器事件
    if (monthSelect) {
        monthSelect.addEventListener('change', function() {
            const selectedMonth = this.value;
            const currentYear = yearSelect ? yearSelect.value : new Date().getFullYear();
            updateDateSelection(currentYear, selectedMonth);
        });
    }
});

// 更新日期选择
function updateDateSelection(year, month) {
    const currentPath = window.location.pathname;
    window.location.href = `${currentPath}?year=${year}&month=${month}`;
}

// 通用AJAX请求函数
function makeRequest(url, method = 'GET', data = null) {
    const options = {
        method: method,
        headers: {
            'Content-Type': 'application/json',
        }
    };

    if (data) {
        options.body = JSON.stringify(data);
    }

    return fetch(url, options)
        .then(response => response.json())
        .catch(error => {
            console.error('Request error:', error);
            throw error;
        });
}

// 用户管理功能

// 显示添加用户模态框
function showAddUserModal() {
    const modal = `
        <div class="modal is-active" id="addUserModal">
            <div class="modal-background" onclick="closeModal('addUserModal')"></div>
            <div class="modal-card">
                <header class="modal-card-head">
                    <p class="modal-card-title">添加用户</p>
                    <button class="delete" aria-label="close" onclick="closeModal('addUserModal')"></button>
                </header>
                <section class="modal-card-body">
                    <form id="addUserForm">
                        <div class="field">
                            <label class="label" for="userName">姓名</label>
                            <div class="control">
                                <input class="input" type="text" id="userName" required>
                            </div>
                        </div>
                        <div class="field">
                            <label class="label" for="userJobNumber">工号</label>
                            <div class="control">
                                <input class="input" type="text" id="userJobNumber" required>
                            </div>
                        </div>
                        <div class="field">
                            <label class="label" for="userRole">角色</label>
                            <div class="control">
                                <div class="select is-fullwidth">
                                    <select id="userRole" required>
                                        <option value="">请选择角色</option>
                                        <option value="staff">员工</option>
                                        <option value="director">主任</option>
                                        <option value="admin">管理员</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="field">
                            <label class="label" for="userPassword">密码</label>
                            <div class="control">
                                <input class="input" type="password" id="userPassword" required>
                            </div>
                        </div>
                    </form>
                </section>
                <footer class="modal-card-foot">
                    <button class="button is-primary" onclick="createUser()">创建</button>
                    <button class="button" onclick="closeModal('addUserModal')">取消</button>
                </footer>
            </div>
        </div>
    `;

    // 移除已存在的模态框
    const existingModal = document.getElementById('addUserModal');
    if (existingModal) {
        existingModal.remove();
    }

    // 添加模态框到页面
    document.body.insertAdjacentHTML('beforeend', modal);
}

// 创建用户
function createUser() {
    const name = document.getElementById('userName').value;
    const jobNumber = document.getElementById('userJobNumber').value;
    const role = document.getElementById('userRole').value;
    const password = document.getElementById('userPassword').value;

    if (!name || !jobNumber || !role || !password) {
        alert('请填写所有字段');
        return;
    }

    makeRequest('/admin/users', 'POST', {
        name: name,
        jobNumber: jobNumber,
        role: role,
        password: password
    })
    .then(data => {
        if (data.success) {
            alert('用户创建成功');
            closeModal('addUserModal');
            location.reload();
        } else {
            alert('创建失败: ' + data.error);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('创建用户时发生错误');
    });
}

// 编辑用户
function editUser(userid) {
    // 首先获取用户信息
    makeRequest(`/admin/users`, 'GET')
    .then(users => {
        const user = users.find(u => u.userid == userid);
        if (!user) {
            alert('用户不存在');
            return;
        }

        const modal = `
            <div class="modal is-active" id="editUserModal">
                <div class="modal-background" onclick="closeModal('editUserModal')"></div>
                <div class="modal-card">
                    <header class="modal-card-head">
                        <p class="modal-card-title">编辑用户</p>
                        <button class="delete" aria-label="close" onclick="closeModal('editUserModal')"></button>
                    </header>
                    <section class="modal-card-body">
                        <form id="editUserForm">
                            <div class="field">
                                <label class="label" for="editUserName">姓名</label>
                                <div class="control">
                                    <input class="input" type="text" id="editUserName" value="${user.name}" required>
                                </div>
                            </div>
                            <div class="field">
                                <label class="label" for="editUserJobNumber">工号</label>
                                <div class="control">
                                    <input class="input" type="text" id="editUserJobNumber" value="${user.job_number}" required>
                                </div>
                            </div>
                            <div class="field">
                                <label class="label" for="editUserRole">角色</label>
                                <div class="control">
                                    <div class="select is-fullwidth">
                                        <select id="editUserRole" required>
                                            <option value="staff" ${user.role === 'staff' ? 'selected' : ''}>员工</option>
                                            <option value="director" ${user.role === 'director' ? 'selected' : ''}>主任</option>
                                            <option value="admin" ${user.role === 'admin' ? 'selected' : ''}>管理员</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </section>
                    <footer class="modal-card-foot">
                        <button class="button is-primary" onclick="updateUser(${userid})">更新</button>
                        <button class="button" onclick="closeModal('editUserModal')">取消</button>
                    </footer>
                </div>
            </div>
        `;

        // 移除已存在的模态框
        const existingModal = document.getElementById('editUserModal');
        if (existingModal) {
            existingModal.remove();
        }

        // 添加模态框到页面
        document.body.insertAdjacentHTML('beforeend', modal);
    })
    .catch(error => {
        console.error('Error:', error);
        alert('获取用户信息时发生错误');
    });
}

// 更新用户
function updateUser(userid) {
    const name = document.getElementById('editUserName').value;
    const jobNumber = document.getElementById('editUserJobNumber').value;
    const role = document.getElementById('editUserRole').value;

    if (!name || !jobNumber || !role) {
        alert('请填写所有字段');
        return;
    }

    makeRequest(`/admin/users/${userid}`, 'PUT', {
        name: name,
        jobNumber: jobNumber,
        role: role
    })
    .then(data => {
        if (data.success) {
            alert('用户信息更新成功');
            closeModal('editUserModal');
            location.reload();
        } else {
            alert('更新失败: ' + data.error);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('更新用户时发生错误');
    });
}

// 删除用户
function deleteUser(userid) {
    if (confirm('确定要删除这个用户吗？此操作不可恢复！')) {
        makeRequest(`/admin/users/${userid}`, 'DELETE')
        .then(data => {
            if (data.success) {
                alert('用户删除成功');
                location.reload();
            } else {
                alert('删除失败: ' + data.error);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('删除用户时发生错误');
        });
    }
}

// 重置密码
function resetPassword(userid) {
    const newPassword = prompt('请输入新密码:');
    if (newPassword && newPassword.trim()) {
        makeRequest(`/admin/users/${userid}/reset-password`, 'POST', {
            newPassword: newPassword.trim()
        })
        .then(data => {
            if (data.success) {
                alert('密码重置成功');
            } else {
                alert('重置失败: ' + data.error);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('重置密码时发生错误');
        });
    }
}

// 显示批量导入模态框
function showImportModal() {
    const modal = `
        <div class="modal is-active" id="importModal">
            <div class="modal-background" onclick="closeModal('importModal')"></div>
            <div class="modal-card" style="width: 90%; max-width: 800px;">
                <header class="modal-card-head">
                    <p class="modal-card-title">批量导入用户</p>
                    <button class="delete" aria-label="close" onclick="closeModal('importModal')"></button>
                </header>
                <section class="modal-card-body">
                    <div class="notification is-info">
                        <h6 class="title is-6">导入说明：</h6>
                        <ul>
                            <li>支持Excel文件格式 (.xlsx, .xls)</li>
                            <li>文件大小不超过5MB</li>
                            <li>请使用提供的模板格式</li>
                            <li>工号必须唯一，不能与现有用户重复</li>
                        </ul>
                    </div>

                    <div class="field">
                        <label class="label">1. 下载导入模板</label>
                        <div class="control">
                            <a href="/admin/users/template" class="button is-primary is-outlined" download>
                                <span class="icon">
                                    <i class="fas fa-download"></i>
                                </span>
                                <span>下载Excel模板</span>
                            </a>
                        </div>
                    </div>

                    <div class="field">
                        <label class="label" for="importFile">2. 选择要导入的Excel文件</label>
                        <div class="control">
                            <input class="input" type="file" id="importFile" accept=".xlsx,.xls">
                        </div>
                    </div>

                    <div id="importProgress" class="is-hidden">
                        <progress class="progress is-primary" max="100">正在导入...</progress>
                    </div>

                    <div id="importResult" class="is-hidden">
                        <div class="notification" id="importAlert">
                            <div id="importMessage"></div>
                            <div id="importDetails" class="mt-2"></div>
                        </div>
                    </div>
                </section>
                <footer class="modal-card-foot">
                    <button class="button is-success" onclick="importUsers()" id="importBtn">
                        <span class="icon">
                            <i class="fas fa-upload"></i>
                        </span>
                        <span>开始导入</span>
                    </button>
                    <button class="button" onclick="closeModal('importModal')">取消</button>
                </footer>
            </div>
        </div>
    `;

    // 移除已存在的模态框
    const existingModal = document.getElementById('importModal');
    if (existingModal) {
        existingModal.remove();
    }

    // 添加模态框到页面
    document.body.insertAdjacentHTML('beforeend', modal);
}

// 批量导入用户
function importUsers() {
    const fileInput = document.getElementById('importFile');
    const file = fileInput.files[0];

    if (!file) {
        alert('请选择要导入的Excel文件');
        return;
    }

    // 检查文件类型
    const allowedTypes = [
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'application/vnd.ms-excel'
    ];

    if (!allowedTypes.includes(file.type)) {
        alert('请选择Excel文件 (.xlsx 或 .xls)');
        return;
    }

    // 检查文件大小 (5MB)
    if (file.size > 5 * 1024 * 1024) {
        alert('文件大小不能超过5MB');
        return;
    }

    // 显示进度条
    document.getElementById('importProgress').classList.remove('is-hidden');
    document.getElementById('importResult').classList.add('is-hidden');
    document.getElementById('importBtn').disabled = true;

    // 创建FormData
    const formData = new FormData();
    formData.append('userFile', file);

    // 发送请求
    fetch('/admin/users/import', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        // 隐藏进度条
        document.getElementById('importProgress').classList.add('is-hidden');
        document.getElementById('importBtn').disabled = false;

        // 显示结果
        const resultDiv = document.getElementById('importResult');
        const alertDiv = document.getElementById('importAlert');
        const messageDiv = document.getElementById('importMessage');
        const detailsDiv = document.getElementById('importDetails');

        resultDiv.classList.remove('is-hidden');

        if (data.success) {
            alertDiv.className = 'notification is-success';
            messageDiv.textContent = data.message;

            if (data.details && data.details.errorCount > 0) {
                let detailsHtml = `<strong>导入详情：</strong><br>`;
                detailsHtml += `成功：${data.details.successCount} 个用户<br>`;
                detailsHtml += `失败：${data.details.errorCount} 个用户<br>`;

                if (data.details.errors.length > 0) {
                    detailsHtml += `<br><strong>错误信息：</strong><br>`;
                    data.details.errors.forEach(error => {
                        detailsHtml += `• ${error}<br>`;
                    });
                }

                detailsDiv.innerHTML = detailsHtml;
            } else {
                detailsDiv.innerHTML = `<strong>所有用户导入成功！</strong>`;
            }

            // 3秒后刷新页面
            setTimeout(() => {
                location.reload();
            }, 3000);
        } else {
            alertDiv.className = 'notification is-danger';
            messageDiv.textContent = '导入失败：' + data.error;
            detailsDiv.innerHTML = '';
        }
    })
    .catch(error => {
        console.error('Import error:', error);

        // 隐藏进度条
        document.getElementById('importProgress').classList.add('is-hidden');
        document.getElementById('importBtn').disabled = false;

        // 显示错误
        const resultDiv = document.getElementById('importResult');
        const alertDiv = document.getElementById('importAlert');
        const messageDiv = document.getElementById('importMessage');

        resultDiv.classList.remove('is-hidden');
        alertDiv.className = 'notification is-danger';
        messageDiv.textContent = '导入过程中发生错误，请稍后重试';
    });
}
