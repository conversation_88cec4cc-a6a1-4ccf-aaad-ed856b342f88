<%- include('../partials/header') %>

<div class="columns">
    <div class="column">
        <h2 class="title is-2">系统管理面板</h2>
        <p class="has-text-grey">当前查看：<%= currentYear %>年<%= currentMonth %>月</p>
    </div>
</div>

<div class="columns is-multiline mb-4">
    <div class="column is-2">
        <div class="card has-text-centered">
            <div class="card-content">
                <h5 class="title is-5">总用户数</h5>
                <h3 class="title is-3 has-text-primary"><%= stats.totalUsers %></h3>
                <p class="subtitle is-6">人</p>
            </div>
        </div>
    </div>
    <div class="column is-2">
        <div class="card has-text-centered">
            <div class="card-content">
                <h5 class="title is-5">员工数</h5>
                <h3 class="title is-3 has-text-info"><%= stats.totalStaff %></h3>
                <p class="subtitle is-6">人</p>
            </div>
        </div>
    </div>
    <div class="column is-2">
        <div class="card has-text-centered">
            <div class="card-content">
                <h5 class="title is-5">主任数</h5>
                <h3 class="title is-3 has-text-warning"><%= stats.totalDirectors %></h3>
                <p class="subtitle is-6">人</p>
            </div>
        </div>
    </div>
    <div class="column is-2">
        <div class="card has-text-centered">
            <div class="card-content">
                <h5 class="title is-5">管理员数</h5>
                <h3 class="title is-3 has-text-danger"><%= stats.totalAdmins %></h3>
                <p class="subtitle is-6">人</p>
            </div>
        </div>
    </div>
    <div class="column is-2">
        <div class="card has-text-centered">
            <div class="card-content">
                <h5 class="title is-5">本月值班</h5>
                <h3 class="title is-3 has-text-success"><%= stats.totalDutyDays %></h3>
                <p class="subtitle is-6">天</p>
            </div>
        </div>
    </div>
    <div class="column is-2">
        <div class="card has-text-centered">
            <div class="card-content">
                <h5 class="title is-5">平均值班</h5>
                <h3 class="title is-3 has-text-grey"><%= stats.avgDutyPerStaff %></h3>
                <p class="subtitle is-6">天/人</p>
            </div>
        </div>
    </div>
</div>

<div class="columns">
    <div class="column is-half">
        <div class="card">
            <div class="card-header">
                <div class="level">
                    <div class="level-left">
                        <h5 class="title is-5">用户管理</h5>
                    </div>
                    <div class="level-right">
                        <div class="field is-grouped">
                            <p class="control">
                                <a href="/admin/users-page" class="button is-primary is-outlined is-small">管理用户</a>
                            </p>
                            <p class="control">
                                <button class="button is-success is-small" onclick="showImportModal()">批量导入</button>
                            </p>
                            <p class="control">
                                <button class="button is-primary is-small" onclick="showAddUserModal()">添加用户</button>
                            </p>
                        </div>
                    </div>
                </div>
            </div>
            <div class="card-content">
                <div class="table-container">
                    <table class="table is-striped is-narrow is-fullwidth">
                        <thead>
                            <tr>
                                <th>工号</th>
                                <th>姓名</th>
                                <th>角色</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <% users.all.forEach(user => { %>
                            <tr>
                                <td><%= user.job_number %></td>
                                <td><%= user.name %></td>
                                <td>
                                    <% if (user.role === 'admin') { %>
                                    <span class="tag is-danger">管理员</span>
                                    <% } else if (user.role === 'director') { %>
                                    <span class="tag is-warning">主任</span>
                                    <% } else { %>
                                    <span class="tag is-info">员工</span>
                                    <% } %>
                                </td>
                                <td>
                                    <div class="field is-grouped">
                                        <p class="control">
                                            <button class="button is-primary is-outlined is-small" onclick="editUser(<%= user.userid %>)">编辑</button>
                                        </p>
                                        <p class="control">
                                            <button class="button is-warning is-outlined is-small" onclick="resetPassword(<%= user.userid %>)">重置密码</button>
                                        </p>
                                        <% if (user.userid != locals.user.userid) { %>
                                        <p class="control">
                                            <button class="button is-danger is-outlined is-small" onclick="deleteUser(<%= user.userid %>)">删除</button>
                                        </p>
                                        <% } %>
                                    </div>
                                </td>
                            </tr>
                            <% }) %>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <div class="column is-half">
        <div class="card">
            <div class="card-header">
                <div class="level">
                    <div class="level-left">
                        <h5 class="title is-5">值班记录</h5>
                    </div>
                    <div class="level-right">
                        <div class="field is-grouped">
                            <div class="control">
                                <div class="select is-small">
                                    <select id="yearSelect">
                                        <%
                                        const startYear = 2020;
                                        const endYear = new Date().getFullYear() + 1;
                                        for(let year = endYear; year >= startYear; year--) { %>
                                        <option value="<%= year %>" <%= year === currentYear ? 'selected' : '' %>><%= year %>年</option>
                                        <% } %>
                                    </select>
                                </div>
                            </div>
                            <div class="control">
                                <div class="select is-small">
                                    <select id="monthSelect">
                                        <% for(let i = 1; i <= 12; i++) { %>
                                        <option value="<%= i %>" <%= i === currentMonth ? 'selected' : '' %>><%= i %>月</option>
                                        <% } %>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="card-content">
                <% if (records.length > 0) { %>
                <div class="table-container">
                    <table class="table is-striped is-narrow is-fullwidth">
                        <thead>
                            <tr>
                                <th>姓名</th>
                                <th>值班日期</th>
                                <th>调休日期</th>
                                <th>是否自定义调休</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <% records.slice(0, 10).forEach(record => { %>
                            <tr>
                                <td><%= record.name %></td>
                                <td><%= (() => {
                                    const date = new Date(record.duty_date);
                                    const year = date.getFullYear();
                                    const month = String(date.getMonth() + 1).padStart(2, '0');
                                    const day = String(date.getDate()).padStart(2, '0');
                                    return `${year}-${month}-${day}`;
                                })() %></td>
                                <td><%= record.leave_date ? (() => {
                                    const date = new Date(record.leave_date);
                                    const year = date.getFullYear();
                                    const month = String(date.getMonth() + 1).padStart(2, '0');
                                    const day = String(date.getDate()).padStart(2, '0');
                                    return `${year}-${month}-${day}`;
                                })() : '未设置' %></td>
                                <td><%= record.is_custom_leave ? '是' : '否' %></td>
                                <td>
                                    <div class="field is-grouped">
                                        <p class="control">
                                            <button class="button is-primary is-outlined is-small" onclick="editRecord(<%= record.record_id %>)">编辑</button>
                                        </p>
                                        <p class="control">
                                            <button class="button is-danger is-outlined is-small" onclick="deleteRecord(<%= record.record_id %>)">删除</button>
                                        </p>
                                    </div>
                                </td>
                            </tr>
                            <% }) %>
                        </tbody>
                    </table>
                </div>
                <% if (records.length > 10) { %>
                <p class="has-text-grey has-text-centered">显示前10条记录，共<%= records.length %>条</p>
                <% } %>
                <% } else { %>
                <p class="has-text-centered has-text-grey">本月暂无值班记录</p>
                <% } %>
            </div>
        </div>
    </div>
</div>

<%- include('../partials/footer') %>
